#!/usr/bin/env python3
"""
Test script to verify the bug fixes and improvements.
"""

import sys
import os
import tempfile
import shutil

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all imports work correctly."""
    print("Testing imports...")
    
    try:
        from main import main, parse_arguments, setup_environment
        print("✓ Main module imports successful")
        
        from agents.character_consistency import CharacterConsistencyAgent
        print("✓ CharacterConsistencyAgent import successful")
        
        from models.schema import Story, Scene, CharacterConsistencyData
        print("✓ Schema models import successful")
        
        return True
    except Exception as e:
        print(f"✗ Import error: {e}")
        return False

def test_character_consistency_agent():
    """Test CharacterConsistencyAgent initialization and basic functionality."""
    print("\nTesting CharacterConsistencyAgent...")
    
    try:
        from agents.character_consistency import CharacterConsistencyAgent
        from models.schema import Story, Scene
        
        # Test initialization
        agent = CharacterConsistencyAgent(verbose=False)
        print("✓ CharacterConsistencyAgent initialization successful")
        
        # Test with empty story
        story = Story(title="Test Story", scenes=[])
        
        # Test the new parameter in process_story_for_consistency
        with tempfile.TemporaryDirectory() as temp_dir:
            # This should not crash even with empty story
            result = agent.process_story_for_consistency(
                story, 
                temp_dir, 
                image_style='realistic',
                generate_individual_references=False
            )
            print("✓ process_story_for_consistency with new parameter works")
        
        return True
    except Exception as e:
        print(f"✗ CharacterConsistencyAgent error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_function_structure():
    """Test that the main function structure is valid."""
    print("\nTesting main function structure...")
    
    try:
        import argparse
        from main import parse_arguments, get_agent_config
        
        # Test argument parsing
        # We can't actually run parse_arguments without command line args,
        # but we can test the function exists and is callable
        assert callable(parse_arguments), "parse_arguments should be callable"
        print("✓ parse_arguments function is callable")
        
        # Test get_agent_config function
        mock_args = argparse.Namespace()
        mock_args.researcher_model = None
        mock_args.researcher_provider = None
        
        model, provider = get_agent_config(mock_args, 'researcher', 'gpt-4o-mini', 'openai')
        assert model == 'gpt-4o-mini', f"Expected 'gpt-4o-mini', got '{model}'"
        assert provider == 'openai', f"Expected 'openai', got '{provider}'"
        print("✓ get_agent_config function works correctly")
        
        return True
    except Exception as e:
        print(f"✗ Main function structure error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_type_annotations():
    """Test that type annotations are properly added."""
    print("\nTesting type annotations...")
    
    try:
        from main import setup_environment, parse_arguments, get_latest_story_dir, create_story_directory, main
        from agents.character_consistency import CharacterConsistencyAgent
        
        # Check if functions have proper annotations
        import inspect
        
        # Test setup_environment
        sig = inspect.signature(setup_environment)
        assert sig.return_annotation is not None or sig.return_annotation == type(None), "setup_environment should have return annotation"
        print("✓ setup_environment has proper type annotations")
        
        # Test parse_arguments
        sig = inspect.signature(parse_arguments)
        assert sig.return_annotation is not None, "parse_arguments should have return annotation"
        print("✓ parse_arguments has proper type annotations")
        
        # Test CharacterConsistencyAgent.__init__
        sig = inspect.signature(CharacterConsistencyAgent.__init__)
        assert sig.return_annotation is not None or sig.return_annotation == type(None), "CharacterConsistencyAgent.__init__ should have return annotation"
        print("✓ CharacterConsistencyAgent.__init__ has proper type annotations")
        
        return True
    except Exception as e:
        print(f"✗ Type annotation error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main_test():
    """Run all tests."""
    print("Running bug fix and improvement tests...\n")
    
    tests = [
        test_imports,
        test_character_consistency_agent,
        test_main_function_structure,
        test_type_annotations,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The bug fixes and improvements are working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please review the errors above.")
        return False

if __name__ == "__main__":
    success = main_test()
    sys.exit(0 if success else 1)
